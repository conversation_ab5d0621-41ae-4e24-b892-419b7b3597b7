{"timestamp": "2025-08-05T09:04:21.607419", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:21.612429", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:21.612429", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:21.612429", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:23.145736", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:25.973270", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:25.973270", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:26.279899", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:26.353017", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:26.446066", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:26.524193", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:04:26.603601", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 33548, "thread_name": "MainThread", "process": 3048}
{"timestamp": "2025-08-05T09:05:58.558271", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:05:58.560720", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:05:58.561254", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:05:58.563012", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:05:59.058906", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:05:59.748555", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:05:59.748555", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:06:00.009882", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:06:00.082791", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:06:00.157923", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:06:00.229665", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:06:00.292229", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:06:04.405824", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:06:04.828241", "level": "INFO", "logger": "app.core.websocket_manager", "message": "WebSocket后台任务已启动", "module": "websocket_manager", "function": "start_background_tasks", "line": 114, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:06:04.828241", "level": "INFO", "logger": "app.services.monitoring_service", "message": "系统监控已启动", "module": "monitoring_service", "function": "start_monitoring", "line": 50, "thread": 38864, "thread_name": "MainThread", "process": 14972}
{"timestamp": "2025-08-05T09:07:45.630076", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:45.635249", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:45.635249", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:45.635249", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:46.761623", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:48.352342", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:48.352342", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:48.565669", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:48.648594", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:48.730278", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:48.821026", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:48.898470", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:52.958212", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:53.421038", "level": "INFO", "logger": "app.core.websocket_manager", "message": "WebSocket后台任务已启动", "module": "websocket_manager", "function": "start_background_tasks", "line": 114, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:07:53.421038", "level": "INFO", "logger": "app.services.monitoring_service", "message": "系统监控已启动", "module": "monitoring_service", "function": "start_monitoring", "line": 50, "thread": 24768, "thread_name": "MainThread", "process": 37340}
{"timestamp": "2025-08-05T09:18:35.862682", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:35.867684", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:35.867684", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:35.867684", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:36.368780", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:37.082620", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:37.082620", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:37.247093", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:37.323929", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:37.383279", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:37.484241", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:37.572257", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:41.651816", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:41.904109", "level": "INFO", "logger": "app.core.websocket_manager", "message": "WebSocket后台任务已启动", "module": "websocket_manager", "function": "start_background_tasks", "line": 114, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:18:41.908612", "level": "INFO", "logger": "app.services.monitoring_service", "message": "系统监控已启动", "module": "monitoring_service", "function": "start_monitoring", "line": 50, "thread": 18596, "thread_name": "MainThread", "process": 20904}
{"timestamp": "2025-08-05T09:19:55.388248", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:55.388248", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:55.388248", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:55.388248", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:55.865543", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:56.573291", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:56.573291", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:56.774215", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:56.831293", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:56.907449", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:56.991302", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:19:57.050620", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:20:01.127112", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 14636, "thread_name": "MainThread", "process": 18520}
{"timestamp": "2025-08-05T09:20:38.488204", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:38.493942", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:38.493942", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:38.493942", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:39.072595", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:39.940951", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:39.940951", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:40.008515", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:40.087604", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:40.151093", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:40.212633", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:40.274045", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:44.316893", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:44.514030", "level": "INFO", "logger": "app.core.websocket_manager", "message": "WebSocket后台任务已启动", "module": "websocket_manager", "function": "start_background_tasks", "line": 114, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:20:44.518033", "level": "INFO", "logger": "app.services.monitoring_service", "message": "系统监控已启动", "module": "monitoring_service", "function": "start_monitoring", "line": 50, "thread": 2736, "thread_name": "MainThread", "process": 27148}
{"timestamp": "2025-08-05T09:23:22.219997", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:22.222829", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:22.228002", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:22.228002", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:23.266548", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:24.913019", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:24.913019", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:25.169165", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:25.285207", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:25.387100", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:25.532287", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:25.793451", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:29.856461", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:30.312411", "level": "INFO", "logger": "app.core.websocket_manager", "message": "WebSocket后台任务已启动", "module": "websocket_manager", "function": "start_background_tasks", "line": 114, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:23:30.312996", "level": "INFO", "logger": "app.services.monitoring_service", "message": "系统监控已启动", "module": "monitoring_service", "function": "start_monitoring", "line": 50, "thread": 36800, "thread_name": "MainThread", "process": 5696}
{"timestamp": "2025-08-05T09:25:10.722519", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:10.727682", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:10.731188", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:10.732615", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:11.768656", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:13.595993", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:13.596936", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:13.811846", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:13.903332", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:13.988415", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:14.075792", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:14.157673", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:18.226319", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:18.612201", "level": "INFO", "logger": "app.core.websocket_manager", "message": "WebSocket后台任务已启动", "module": "websocket_manager", "function": "start_background_tasks", "line": 114, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:25:18.638485", "level": "INFO", "logger": "app.services.monitoring_service", "message": "系统监控已启动", "module": "monitoring_service", "function": "start_monitoring", "line": 50, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:29:49.374216", "level": "ERROR", "logger": "main", "message": "请求验证错误: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}]", "module": "main", "function": "validation_exception_handler", "line": 82, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:29:49.374216", "level": "ERROR", "logger": "main", "message": "请求URL: http://localhost:8000/api/v1/auth/login", "module": "main", "function": "validation_exception_handler", "line": 83, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:29:49.374216", "level": "ERROR", "logger": "main", "message": "请求方法: POST", "module": "main", "function": "validation_exception_handler", "line": 84, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:29:49.377213", "level": "ERROR", "logger": "main", "message": "请求体: {\"username\":\"admin\",\"password\":\"admin123\"}", "module": "main", "function": "validation_exception_handler", "line": 87, "thread": 32752, "thread_name": "MainThread", "process": 25540}
{"timestamp": "2025-08-05T09:51:17.789981", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:17.791973", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:17.791973", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:17.792960", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:18.234762", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:18.733230", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:18.733230", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:18.946188", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:19.024359", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:19.113134", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:19.184236", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:19.272877", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:23.373765", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:23.563540", "level": "INFO", "logger": "app.core.websocket_manager", "message": "WebSocket后台任务已启动", "module": "websocket_manager", "function": "start_background_tasks", "line": 114, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:51:23.564495", "level": "INFO", "logger": "app.services.monitoring_service", "message": "系统监控已启动", "module": "monitoring_service", "function": "start_monitoring", "line": 50, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:11.774185", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取用户 na10000014 的服务条线", "module": "auth", "function": "get_user_service_lines", "line": 23, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:11.896485", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "用户 na10000014 的服务条线: ['金租', '商租', '汽租']", "module": "auth", "function": "get_user_service_lines", "line": 40, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.005253", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取到用户信息: {'UserID': 'na10000014', 'username': 'LiaoFang', 'name': '廖芳', 'role': 3.0, 'department_name': '业务二部', 'company_name': '数字金服', 'LaborCost': 20.0}", "module": "auth", "function": "get_me", "line": 151, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.006167", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取用户 na10000014 的服务条线", "module": "auth", "function": "get_user_service_lines", "line": 23, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.125477", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "用户 na10000014 的服务条线: ['金租', '商租', '汽租']", "module": "auth", "function": "get_user_service_lines", "line": 40, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.231168", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取用户 na10000014 的服务条线", "module": "auth", "function": "get_user_service_lines", "line": 23, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.361104", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "用户 na10000014 的服务条线: ['金租', '商租', '汽租']", "module": "auth", "function": "get_user_service_lines", "line": 40, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.362100", "level": "INFO", "logger": "root", "message": "接收到请求: {'queryString': {}}", "module": "index", "function": "main_handler", "line": 656, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.434478", "level": "INFO", "logger": "root", "message": "执行查询: \n                    SELECT *, CAST(annual_investment_plan AS DECIMAL(10,2)) AS annual_investment_value\n                    FROM Project_Account_Book\n                ", "module": "index", "function": "main_handler", "line": 695, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.435466", "level": "INFO", "logger": "root", "message": "查询参数: []", "module": "index", "function": "main_handler", "line": 696, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.530683", "level": "INFO", "logger": "root", "message": "查询到的项目数量: 216", "module": "index", "function": "main_handler", "line": 700, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.531680", "level": "INFO", "logger": "root", "message": "查询ITBP团队成员，今天: 2025-08-05, 一年前: 2024-08-05", "module": "index", "function": "get_itbp_team_members", "line": 270, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.549618", "level": "INFO", "logger": "root", "message": "获取到 63 个ITBP团队成员", "module": "index", "function": "get_itbp_team_members", "line": 311, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.549618", "level": "INFO", "logger": "root", "message": "共有 21 个投资主体有团队成员数据", "module": "index", "function": "get_itbp_team_members", "line": 326, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.551604", "level": "INFO", "logger": "root", "message": "实施阶段项目: 诉讼管理系统（一期）, 编号: C202500006, 阶段: 项目实施, 状态: 未逾期, 投资金额: 83.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.551604", "level": "INFO", "logger": "root", "message": "实施阶段项目: 2024业务中台风控模块升级优化项目, 编号: C202500011, 阶段: 项目实施, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.552599", "level": "INFO", "logger": "root", "message": "实施阶段项目: 业务中台系统升级服务, 编号: C202500054, 阶段: 任务采购, 状态: 未逾期, 投资金额: 30.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.552599", "level": "INFO", "logger": "root", "message": "实施阶段项目: 个贷不良业务管理系统_电催, 编号: C202500090, 阶段: 项目实施, 状态: 未逾期, 投资金额: 100.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.553595", "level": "INFO", "logger": "root", "message": "实施阶段项目: 个贷不良业务管理系统_接征信, 编号: C202500091, 阶段: 项目实施, 状态: 未逾期, 投资金额: 125.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.553595", "level": "INFO", "logger": "root", "message": "实施阶段项目: IFRS17项目, 编号: C202500095, 阶段: 项目实施, 状态: 未逾期, 投资金额: 400.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.553595", "level": "INFO", "logger": "root", "message": "实施阶段项目: 农险一体化平台新建项目, 编号: C202500103, 阶段: 任务采购, 状态: 逾期, 投资金额: 350.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.554593", "level": "INFO", "logger": "root", "message": "实施阶段项目: 客户经营及理赔类系统建设, 编号: C202500104, 阶段: 任务采购, 状态: 未逾期, 投资金额: 230.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.554593", "level": "INFO", "logger": "root", "message": "实施阶段项目: 企业征信服务平台, 编号: C202500150, 阶段: 项目实施, 状态: 未逾期, 投资金额: 198.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.554593", "level": "INFO", "logger": "root", "message": "实施阶段项目: 库存融资业务系统后续开发, 编号: C202500132, 阶段: 项目实施, 状态: 未逾期, 投资金额: 120.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.555589", "level": "INFO", "logger": "root", "message": "实施阶段项目: 同盾调度平台, 编号: C202500133, 阶段: 项目实施, 状态: 未逾期, 投资金额: 115.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.555589", "level": "INFO", "logger": "root", "message": "实施阶段项目: 账号安全管理系统, 编号: C202500001, 阶段: 项目实施, 状态: 未逾期, 投资金额: 44.8", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.555589", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据中台（一期）及管理驾驶舱项目, 编号: C202500002, 阶段: 项目实施, 状态: 未逾期, 投资金额: 140.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.555589", "level": "INFO", "logger": "root", "message": "实施阶段项目: 融资管理系统项目, 编号: C202500003, 阶段: 项目结项, 状态: 未逾期, 投资金额: 64.4", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.555589", "level": "INFO", "logger": "root", "message": "实施阶段项目: EAS财务系统升级金蝶云星瀚项目, 编号: C202500004, 阶段: 已完成, 状态: 未逾期, 投资金额: 61.6", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.556586", "level": "INFO", "logger": "root", "message": "实施阶段项目: 新增业务中台不良资产过程管理及清收管控等功能项目, 编号: C202500005, 阶段: 项目结项, 状态: 未逾期, 投资金额: 45.1", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.556586", "level": "INFO", "logger": "root", "message": "实施阶段项目: 风险资产责任追究系统定制化开发升级项目, 编号: C202500007, 阶段: 项目验收, 状态: 逾期, 投资金额: 45.47", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.556586", "level": "INFO", "logger": "root", "message": "实施阶段项目: 金投集团四会管理系统优化升级项目, 编号: C202500008, 阶段: 已完成, 状态: 未逾期, 投资金额: 13.86", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.557584", "level": "INFO", "logger": "root", "message": "实施阶段项目: 司法大数据应用项目, 编号: C202500009, 阶段: 项目验收, 状态: 逾期, 投资金额: 34.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.557584", "level": "INFO", "logger": "root", "message": "实施阶段项目: 广投保理接入人行征信系统（24年未交付项目）, 编号: C202500016, 阶段: 项目验收, 状态: 未逾期, 投资金额: 56.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.557584", "level": "INFO", "logger": "root", "message": "实施阶段项目: 新资金系统, 编号: C202500020, 阶段: 项目实施, 状态: 未逾期, 投资金额: 88.2", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.557584", "level": "INFO", "logger": "root", "message": "实施阶段项目: 统一数据管理平台, 编号: C202500021, 阶段: 项目验收, 状态: 未逾期, 投资金额: 85.2", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.558579", "level": "INFO", "logger": "root", "message": "实施阶段项目: 移动核保, 编号: C202500022, 阶段: 项目实施, 状态: 未逾期, 投资金额: 76.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.558579", "level": "INFO", "logger": "root", "message": "实施阶段项目: 统一权限管理平台, 编号: C202500024, 阶段: 项目验收, 状态: 未逾期, 投资金额: 56.7", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.559577", "level": "INFO", "logger": "root", "message": "实施阶段项目: 移动行销, 编号: C202500025, 阶段: 项目验收, 状态: 未逾期, 投资金额: 93.6", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.559577", "level": "INFO", "logger": "root", "message": "实施阶段项目: 客户主数据系统, 编号: C202500026, 阶段: 项目实施, 状态: 逾期, 投资金额: 54.6", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.560578", "level": "INFO", "logger": "root", "message": "实施阶段项目: 统一用户管理平台, 编号: C202500027, 阶段: 项目实施, 状态: 未逾期, 投资金额: 49.7", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.561576", "level": "INFO", "logger": "root", "message": "实施阶段项目: 业务开发运维一体化平台, 编号: C202500029, 阶段: 项目验收, 状态: 未逾期, 投资金额: 42.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.562571", "level": "INFO", "logger": "root", "message": "实施阶段项目: 新费用控制系统, 编号: C202500030, 阶段: 项目实施, 状态: 未逾期, 投资金额: 41.4", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.562571", "level": "INFO", "logger": "root", "message": "实施阶段项目: HR人力资源系统, 编号: C202500031, 阶段: 项目实施, 状态: 未逾期, 投资金额: 35.76", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.562571", "level": "INFO", "logger": "root", "message": "实施阶段项目: 费用分摊模块（I4), 编号: C202500032, 阶段: 项目结项, 状态: 未逾期, 投资金额: 31.22", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.563568", "level": "INFO", "logger": "root", "message": "实施阶段项目: 诉讼管理系统, 编号: C202500033, 阶段: 项目验收, 状态: 未逾期, 投资金额: 26.28", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.564566", "level": "INFO", "logger": "root", "message": "实施阶段项目: 电子签章系统, 编号: C202500034, 阶段: 项目实施, 状态: 逾期, 投资金额: 23.9", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.564566", "level": "INFO", "logger": "root", "message": "实施阶段项目: AI影像防篡改与去重系统, 编号: C202500035, 阶段: 项目验收, 状态: 未逾期, 投资金额: 18.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.564566", "level": "INFO", "logger": "root", "message": "实施阶段项目: 新CTI系统, 编号: C202500037, 阶段: 项目实施, 状态: 未逾期, 投资金额: 17.07", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.565560", "level": "INFO", "logger": "root", "message": "实施阶段项目: 协同办公平台（OA系统）系统对接, 编号: C202500039, 阶段: 项目结项, 状态: 未逾期, 投资金额: 15.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.565560", "level": "INFO", "logger": "root", "message": "实施阶段项目: 绩效管理系统, 编号: C202500042, 阶段: 项目实施, 状态: 未逾期, 投资金额: 45.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.565560", "level": "INFO", "logger": "root", "message": "实施阶段项目: 征信系统建设项目, 编号: C202500043, 阶段: 项目实施, 状态: 未逾期, 投资金额: 25.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.566556", "level": "INFO", "logger": "root", "message": "实施阶段项目: 征信系统建设, 编号: C202500044, 阶段: 项目验收, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.566556", "level": "INFO", "logger": "root", "message": "实施阶段项目: 生猪监管二期项目, 编号: C202500045, 阶段: 项目验收, 状态: 逾期, 投资金额: 45.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.566556", "level": "INFO", "logger": "root", "message": "实施阶段项目: 财务资金类监管数据治理开发项目, 编号: C202500046, 阶段: 项目实施, 状态: 逾期, 投资金额: 115.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.566556", "level": "INFO", "logger": "root", "message": "实施阶段项目: 监管报送平台项目, 编号: C202500047, 阶段: 项目实施, 状态: 未逾期, 投资金额: 20.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.567554", "level": "INFO", "logger": "root", "message": "实施阶段项目: 核心业务系统优化升级项目, 编号: C202500050, 阶段: 任务采购, 状态: 未逾期, 投资金额: 110.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.567554", "level": "INFO", "logger": "root", "message": "实施阶段项目: 小贷数据分析服务平台项目, 编号: C202500051, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.567554", "level": "INFO", "logger": "root", "message": "实施阶段项目: 系统运维（费用类）, 编号: C202500053, 阶段: 任务采购, 状态: 未逾期, 投资金额: 16.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.567554", "level": "INFO", "logger": "root", "message": "实施阶段项目: 广投税务管理系统对接项目, 编号: C202500057, 阶段: 已完成, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.568550", "level": "INFO", "logger": "root", "message": "实施阶段项目: 集团网络安全设备加固, 编号: C202500058, 阶段: 任务采购, 状态: 逾期, 投资金额: 64.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.568550", "level": "INFO", "logger": "root", "message": "实施阶段项目: 客户信息共享平台项目, 编号: C202500061, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.568550", "level": "INFO", "logger": "root", "message": "实施阶段项目: 财务系统升级项目, 编号: C202500073, 阶段: 项目实施, 状态: 逾期, 投资金额: 80.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.568550", "level": "INFO", "logger": "root", "message": "实施阶段项目: 经营数据分析平台二期, 编号: C202500074, 阶段: 项目实施, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.569546", "level": "INFO", "logger": "root", "message": "实施阶段项目: 绩效考核管理系统, 编号: C202500075, 阶段: 项目实施, 状态: 未逾期, 投资金额: 10.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.569546", "level": "INFO", "logger": "root", "message": "实施阶段项目: 租赁业务中台系统新增功能开发项目, 编号: C202500076, 阶段: 任务采购, 状态: 逾期, 投资金额: 180.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.570544", "level": "INFO", "logger": "root", "message": "实施阶段项目: 绩效管理平台, 编号: C202500085, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.570544", "level": "INFO", "logger": "root", "message": "实施阶段项目: 各类系统集成接口开发项目, 编号: C202500086, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.570544", "level": "INFO", "logger": "root", "message": "实施阶段项目: 供应链金融服务平台优化项目, 编号: C202500087, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.570544", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_日志管理, 编号: C202500096, 阶段: 任务采购, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.571541", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_国产系统及数据库, 编号: C202500098, 阶段: 任务采购, 状态: 逾期, 投资金额: 70.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.571541", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_监控, 编号: C202500099, 阶段: 任务采购, 状态: 未逾期, 投资金额: 64.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.571541", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_网管, 编号: C202500100, 阶段: 任务采购, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.571541", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_视频会议, 编号: C202500101, 阶段: 项目验收, 状态: 逾期, 投资金额: 90.8", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.572538", "level": "INFO", "logger": "root", "message": "实施阶段项目: 风控数字化建设, 编号: C202500105, 阶段: 任务采购, 状态: 未逾期, 投资金额: 200.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.572538", "level": "INFO", "logger": "root", "message": "实施阶段项目: 平台支撑类应用系统建设, 编号: C202500106, 阶段: 任务采购, 状态: 未逾期, 投资金额: 200.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.572538", "level": "INFO", "logger": "root", "message": "实施阶段项目: 安责险事故预防管理系统, 编号: C202500107, 阶段: 项目实施, 状态: 未逾期, 投资金额: 54.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.573533", "level": "INFO", "logger": "root", "message": "实施阶段项目: 经营分析平台二期建设, 编号: C202500114, 阶段: 项目实施, 状态: 未逾期, 投资金额: 30.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.573533", "level": "INFO", "logger": "root", "message": "实施阶段项目: 智能风控系统, 编号: C202500116, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.573533", "level": "INFO", "logger": "root", "message": "实施阶段项目: 三会会议管理系统, 编号: C202500120, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.573533", "level": "INFO", "logger": "root", "message": "实施阶段项目: 智能风控平台项目, 编号: C202500129, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.573533", "level": "INFO", "logger": "root", "message": "实施阶段项目: 2024年系统开发未结项, 编号: C202500131, 阶段: 项目实施, 状态: 未逾期, 投资金额: 162.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.573533", "level": "INFO", "logger": "root", "message": "实施阶段项目: 财务中台, 编号: C202500135, 阶段: 任务采购, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.573533", "level": "INFO", "logger": "root", "message": "实施阶段项目: 24年系统开发未结项, 编号: C202500140, 阶段: 项目实施, 状态: 未逾期, 投资金额: 100.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.575035", "level": "INFO", "logger": "root", "message": "实施阶段项目: 智能风控系统, 编号: C202500141, 阶段: 项目验收, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.575035", "level": "INFO", "logger": "root", "message": "实施阶段项目: BI系统建设, 编号: C202500143, 阶段: 项目实施, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.575035", "level": "INFO", "logger": "root", "message": "实施阶段项目: 智能外呼系统, 编号: C202500144, 阶段: 项目实施, 状态: 未逾期, 投资金额: 30.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.576035", "level": "INFO", "logger": "root", "message": "实施阶段项目: 财务系统开发, 编号: C202500145, 阶段: 项目实施, 状态: 逾期, 投资金额: 20.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.577031", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据仓库, 编号: C202500147, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.577031", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据建模系统, 编号: C202500148, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.578030", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据治理系统, 编号: C202500149, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.578030", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据质量分析系统, 编号: C202500151, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.578030", "level": "INFO", "logger": "root", "message": "实施阶段项目: 软件测评和安全测评服务采购, 编号: C202500152, 阶段: 项目实施, 状态: 未逾期, 投资金额: 25.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.579026", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件项目尾款, 编号: C202500156, 阶段: 项目验收, 状态: 未逾期, 投资金额: 186.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.579026", "level": "INFO", "logger": "root", "message": "实施阶段项目: 安全设备替换及升级项目（2024）, 编号: C202500157, 阶段: 任务采购, 状态: 未逾期, 投资金额: 48.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.579026", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_网络设备, 编号: C202500161, 阶段: 任务采购, 状态: 未逾期, 投资金额: 72.9", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.580022", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_灾备扩容, 编号: C202500162, 阶段: 任务采购, 状态: 未逾期, 投资金额: 148.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.580022", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_应用监控（硬件）, 编号: C202500163, 阶段: 任务采购, 状态: 逾期, 投资金额: 36.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.580022", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_负载均衡扩容, 编号: C202500165, 阶段: 任务采购, 状态: 未逾期, 投资金额: 45.6", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.580022", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_视频会议硬件, 编号: C202500166, 阶段: 项目验收, 状态: 逾期, 投资金额: 98.29", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.581018", "level": "INFO", "logger": "root", "message": "实施阶段项目: 超融合平台扩容, 编号: C202500170, 阶段: 项目实施, 状态: 未逾期, 投资金额: 84.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.581018", "level": "INFO", "logger": "root", "message": "实施阶段项目: 服务器采购, 编号: C202500172, 阶段: 已完成, 状态: 未逾期, 投资金额: 33.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.581018", "level": "INFO", "logger": "root", "message": "实施阶段项目: 网络安全设备采购, 编号: C202500173, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.581018", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据库服务器采购, 编号: C202500178, 阶段: 项目实施, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.581018", "level": "INFO", "logger": "root", "message": "实施阶段项目: 签名验签服务器采购, 编号: C202500179, 阶段: 已完成, 状态: 未逾期, 投资金额: 20.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.582014", "level": "INFO", "logger": "root", "message": "实施阶段项目: 电子设备采购, 编号: C202500181, 阶段: 已完成, 状态: 未逾期, 投资金额: 5.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.582014", "level": "INFO", "logger": "root", "message": "实施阶段项目: 办公设备, 编号: C202500182, 阶段: 已完成, 状态: 未逾期, 投资金额: 4.9", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.582014", "level": "INFO", "logger": "root", "message": "实施阶段项目: 印控系统升级, 编号: C202500186, 阶段: 项目实施, 状态: 未逾期, 投资金额: 20.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.583012", "level": "INFO", "logger": "root", "message": "实施阶段项目: 国产化电脑替代, 编号: C202500187, 阶段: 任务采购, 状态: 未逾期, 投资金额: 8.3", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.583012", "level": "INFO", "logger": "root", "message": "实施阶段项目: 征信数据处理平台, 编号: C202500189, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.583012", "level": "INFO", "logger": "root", "message": "实施阶段项目: 公共数据处理平台, 编号: C202500190, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.584009", "level": "INFO", "logger": "root", "message": "实施阶段项目: 企业征信服务平台, 编号: C202500191, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.584009", "level": "INFO", "logger": "root", "message": "实施阶段项目: 办公电子设备采购, 编号: C202500192, 阶段: 任务采购, 状态: 逾期, 投资金额: 41.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.584009", "level": "INFO", "logger": "root", "message": "实施阶段项目: 国产化电脑, 编号: C202500193, 阶段: 项目验收, 状态: 未逾期, 投资金额: 25.2", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.585006", "level": "INFO", "logger": "root", "message": "实施阶段项目: 桂惠农项目（“金色乡村”广西农村信用信息系统）, 编号: C202500195, 阶段: 项目验收, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.585006", "level": "INFO", "logger": "root", "message": "实施阶段项目: 桂惠通、桂惠农与智桂通直连项目, 编号: C202500196, 阶段: 项目验收, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.585006", "level": "INFO", "logger": "root", "message": "实施阶段项目: 北部湾经济区金融服务平台二期, 编号: C202500204, 阶段: 项目验收, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.738849", "level": "INFO", "logger": "root", "message": "获取到 701 条工时记录，年份：2025", "module": "index", "function": "get_labor_costs_by_entity", "line": 357, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.760782", "level": "INFO", "logger": "root", "message": "获取到 216 条项目记录用于关联投资主体信息", "module": "index", "function": "get_labor_costs_by_entity", "line": 366, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.760782", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.761777", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.761777", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.761777", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.762774", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.762774", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.762774", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.762774", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.763771", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.763771", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.763771", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.763771", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.764769", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.764769", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.764769", "level": "WARNING", "logger": "root", "message": "项目 C202500211 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.764769", "level": "WARNING", "logger": "root", "message": "项目 C202500211 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.765765", "level": "WARNING", "logger": "root", "message": "项目 C202500211 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.765765", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.766763", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.766763", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.766763", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.767757", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.767757", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.767757", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.768753", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.768753", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.768753", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.768753", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.768753", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.769750", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.769750", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.769750", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.770747", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.770747", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.770747", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.770747", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.771744", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.771744", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.771744", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.771744", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.772743", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.772743", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.772743", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.773737", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.773737", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.773737", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.773737", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.773737", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.775241", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.776240", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.776240", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.777240", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.777240", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.777240", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.778233", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.778233", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.778233", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.779229", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.779229", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.779229", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.779229", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.780226", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.780226", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.780226", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.781224", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.781224", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.781224", "level": "WARNING", "logger": "root", "message": "项目 C202500211 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.782220", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.782220", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.782220", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.782220", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.783216", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.783216", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.783216", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.784215", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.785211", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.786210", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.786210", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.787204", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.788201", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.788201", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.6, 'month': datetime.date(2025, 5, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.789198", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.5, 'month': datetime.date(2025, 4, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.789198", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.4, 'month': datetime.date(2025, 3, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.790194", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.3, 'month': datetime.date(2025, 2, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.791191", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.2, 'month': datetime.date(2025, 1, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.792187", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.6, 'month': datetime.date(2025, 5, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.793184", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.5, 'month': datetime.date(2025, 4, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.794182", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.4, 'month': datetime.date(2025, 3, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.794182", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.3, 'month': datetime.date(2025, 2, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.795178", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.2, 'month': datetime.date(2025, 1, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.796175", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.796175", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.797171", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.797171", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.798168", "level": "INFO", "logger": "root", "message": "计算完成，共有 18 个投资主体的人工费数据", "module": "index", "function": "get_labor_costs_by_entity", "line": 419, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.799165", "level": "INFO", "logger": "root", "message": "获取到 18 个投资主体的人工费数据", "module": "index", "function": "calculate_red_black_board", "line": 476, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.799165", "level": "INFO", "logger": "root", "message": "集团战略部 实施阶段项目数: 3", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.800162", "level": "INFO", "logger": "root", "message": "投资主体: 集团战略部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.800162", "level": "INFO", "logger": "root", "message": "  总项目数: 6, 总预算: 293", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.801158", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 75", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.801158", "level": "INFO", "logger": "root", "message": "  实施项目数: 3, 实施预算: 185", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.802158", "level": "INFO", "logger": "root", "message": "  当年人次合计: 11.6, 人工费: 20万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.802158", "level": "INFO", "logger": "root", "message": "集团法规部 实施阶段项目数: 2", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.803152", "level": "INFO", "logger": "root", "message": "投资主体: 集团法规部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.803152", "level": "INFO", "logger": "root", "message": "  总项目数: 3, 总预算: 150", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.803152", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 34", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.804147", "level": "INFO", "logger": "root", "message": "  实施项目数: 2, 实施预算: 118", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.804147", "level": "INFO", "logger": "root", "message": "  当年人次合计: 14.6, 人工费: 23万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.805144", "level": "INFO", "logger": "root", "message": "集团 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.805144", "level": "INFO", "logger": "root", "message": "投资主体: 集团", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.805144", "level": "INFO", "logger": "root", "message": "  总项目数: 7, 总预算: 543", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.806140", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 364", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.806140", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 64", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.806140", "level": "INFO", "logger": "root", "message": "  当年人次合计: 4.5, 人工费: 7万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.807137", "level": "INFO", "logger": "root", "message": "集团风控部 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.807137", "level": "INFO", "logger": "root", "message": "投资主体: 集团风控部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.807137", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 90", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.808135", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.809130", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 40", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.810127", "level": "INFO", "logger": "root", "message": "  当年人次合计: 1.0, 人工费: 2万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.810127", "level": "INFO", "logger": "root", "message": "集团财务部 实施阶段项目数: 3", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.811123", "level": "INFO", "logger": "root", "message": "投资主体: 集团财务部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.811123", "level": "INFO", "logger": "root", "message": "  总项目数: 6, 总预算: 496", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.812119", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 290", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.812119", "level": "INFO", "logger": "root", "message": "  实施项目数: 3, 实施预算: 176", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.812119", "level": "INFO", "logger": "root", "message": "  当年人次合计: 4.14, 人工费: 7万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.813118", "level": "INFO", "logger": "root", "message": "保理 实施阶段项目数: 5", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.813118", "level": "INFO", "logger": "root", "message": "投资主体: 保理", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.813118", "level": "INFO", "logger": "root", "message": "  总项目数: 15, 总预算: 489", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.814114", "level": "INFO", "logger": "root", "message": "  逾期项目数: 6, 逾期预算: 304", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.814114", "level": "INFO", "logger": "root", "message": "  实施项目数: 5, 实施预算: 64", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.814114", "level": "INFO", "logger": "root", "message": "  当年人次合计: 18.14, 人工费: 32万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.815110", "level": "INFO", "logger": "root", "message": "不动产 实施阶段项目数: 2", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.815110", "level": "INFO", "logger": "root", "message": "投资主体: 不动产", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.815110", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 55", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.815110", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.816108", "level": "INFO", "logger": "root", "message": "  实施项目数: 2, 实施预算: 55", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.816108", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.816108", "level": "INFO", "logger": "root", "message": "资管 实施阶段项目数: 2", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.816108", "level": "INFO", "logger": "root", "message": "投资主体: 资管", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.817104", "level": "INFO", "logger": "root", "message": "  总项目数: 6, 总预算: 591", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.817619", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 195", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.817619", "level": "INFO", "logger": "root", "message": "  实施项目数: 2, 实施预算: 225", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.818137", "level": "INFO", "logger": "root", "message": "  当年人次合计: 1.3, 人工费: 2万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.818137", "level": "INFO", "logger": "root", "message": "财险 实施阶段项目数: 35", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.818137", "level": "INFO", "logger": "root", "message": "投资主体: 财险", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.818137", "level": "INFO", "logger": "root", "message": "  总项目数: 54, 总预算: 4828", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.818137", "level": "INFO", "logger": "root", "message": "  逾期项目数: 14, 逾期预算: 1086", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.819137", "level": "INFO", "logger": "root", "message": "  实施项目数: 35, 实施预算: 3175", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.819137", "level": "INFO", "logger": "root", "message": "  当年人次合计: 5.11, 人工费: 9万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.819137", "level": "INFO", "logger": "root", "message": "征信 实施阶段项目数: 10", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.819137", "level": "INFO", "logger": "root", "message": "投资主体: 征信", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.820133", "level": "INFO", "logger": "root", "message": "  总项目数: 10, 总预算: 264", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.820133", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 42", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.820133", "level": "INFO", "logger": "root", "message": "  实施项目数: 10, 实施预算: 264", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.820133", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0.05, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.820133", "level": "INFO", "logger": "root", "message": "商租 实施阶段项目数: 10", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.821129", "level": "INFO", "logger": "root", "message": "投资主体: 商租", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.821129", "level": "INFO", "logger": "root", "message": "  总项目数: 17, 总预算: 939", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.821129", "level": "INFO", "logger": "root", "message": "  逾期项目数: 3, 逾期预算: 295", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.822127", "level": "INFO", "logger": "root", "message": "  实施项目数: 10, 实施预算: 360", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.822127", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.822127", "level": "INFO", "logger": "root", "message": "汽租 实施阶段项目数: 9", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.823124", "level": "INFO", "logger": "root", "message": "投资主体: 汽租", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.823124", "level": "INFO", "logger": "root", "message": "  总项目数: 16, 总预算: 1001", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.823124", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 180", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.823124", "level": "INFO", "logger": "root", "message": "  实施项目数: 9, 实施预算: 634", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.824120", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0.51, 人工费: 1万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.824120", "level": "INFO", "logger": "root", "message": "集团保全部 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.824120", "level": "INFO", "logger": "root", "message": "投资主体: 集团保全部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.824120", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 125", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.825116", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 80", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.825116", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 45", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.826113", "level": "INFO", "logger": "root", "message": "  当年人次合计: 9.28, 人工费: 15万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.826113", "level": "INFO", "logger": "root", "message": "集团审计部 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.826113", "level": "INFO", "logger": "root", "message": "投资主体: 集团审计部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.827110", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 90", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.827110", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 90", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.827110", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 45", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.827110", "level": "INFO", "logger": "root", "message": "  当年人次合计: 5.36, 人工费: 8万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.828107", "level": "INFO", "logger": "root", "message": "集团办公室 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.828107", "level": "INFO", "logger": "root", "message": "投资主体: 集团办公室", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.828107", "level": "INFO", "logger": "root", "message": "  总项目数: 1, 总预算: 14", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.828107", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.829103", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 14", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.829103", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0.9, 人工费: 2万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.829103", "level": "INFO", "logger": "root", "message": "集团人力部 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.829103", "level": "INFO", "logger": "root", "message": "投资主体: 集团人力部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.830099", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 38", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.830099", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.830099", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.830099", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.831097", "level": "INFO", "logger": "root", "message": "金租 实施阶段项目数: 6", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.831097", "level": "INFO", "logger": "root", "message": "投资主体: 金租", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.831097", "level": "INFO", "logger": "root", "message": "  总项目数: 17, 总预算: 911", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.832093", "level": "INFO", "logger": "root", "message": "  逾期项目数: 9, 逾期预算: 541", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.832093", "level": "INFO", "logger": "root", "message": "  实施项目数: 6, 实施预算: 455", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.832093", "level": "INFO", "logger": "root", "message": "  当年人次合计: 9.5, 人工费: 16万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.832093", "level": "INFO", "logger": "root", "message": "小贷 实施阶段项目数: 4", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.833090", "level": "INFO", "logger": "root", "message": "投资主体: 小贷", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.833090", "level": "INFO", "logger": "root", "message": "  总项目数: 9, 总预算: 195", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.833090", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 35", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.834087", "level": "INFO", "logger": "root", "message": "  实施项目数: 4, 实施预算: 126", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.834087", "level": "INFO", "logger": "root", "message": "  当年人次合计: 2.27, 人工费: 5万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.834087", "level": "INFO", "logger": "root", "message": "集团协同部 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.834087", "level": "INFO", "logger": "root", "message": "投资主体: 集团协同部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.835083", "level": "INFO", "logger": "root", "message": "  总项目数: 1, 总预算: 80", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.835083", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 80", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.835083", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.835083", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.835083", "level": "INFO", "logger": "root", "message": "担保 实施阶段项目数: 4", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.836081", "level": "INFO", "logger": "root", "message": "投资主体: 担保", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.836081", "level": "INFO", "logger": "root", "message": "  总项目数: 17, 总预算: 553", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.836081", "level": "INFO", "logger": "root", "message": "  逾期项目数: 6, 逾期预算: 255", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.836081", "level": "INFO", "logger": "root", "message": "  实施项目数: 4, 实施预算: 50", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.837247", "level": "INFO", "logger": "root", "message": "  当年人次合计: 22.32, 人工费: 44万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.837247", "level": "INFO", "logger": "root", "message": "金服 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.837247", "level": "INFO", "logger": "root", "message": "投资主体: 金服", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.837247", "level": "INFO", "logger": "root", "message": "  总项目数: 3, 总预算: 30", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.838243", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.838243", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.838243", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0.38, 人工费: 1万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.838243", "level": "INFO", "logger": "root", "message": "外部主体 实施阶段项目数: 3", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.838243", "level": "INFO", "logger": "root", "message": "外部主体: 实施项目无投资金额，使用估算: 0万元 (总投资0万元的21.4%)", "module": "index", "function": "calculate_red_black_board", "line": 525, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.839240", "level": "INFO", "logger": "root", "message": "投资主体: 外部主体", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.839240", "level": "INFO", "logger": "root", "message": "  总项目数: 14, 总预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.839240", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.840237", "level": "INFO", "logger": "root", "message": "  实施项目数: 3, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.840237", "level": "INFO", "logger": "root", "message": "  当年人次合计: 39.9, 人工费: 67万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.840237", "level": "INFO", "logger": "root", "message": "None 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.840237", "level": "INFO", "logger": "root", "message": "投资主体: None", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.841234", "level": "INFO", "logger": "root", "message": "  总项目数: 3, 总预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.841234", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.841234", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.842231", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.842231", "level": "INFO", "logger": "root", "message": " 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.842231", "level": "INFO", "logger": "root", "message": "投资主体: ", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.843227", "level": "INFO", "logger": "root", "message": "  总项目数: 1, 总预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.843227", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.843227", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.843227", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.844223", "level": "INFO", "logger": "root", "message": "没有逾期的投资主体共有 8 个", "module": "index", "function": "calculate_red_black_board", "line": 586, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.844223", "level": "INFO", "logger": "root", "message": "按进度推进榜红色标识: 不动产, 集团风控部, 集团办公室", "module": "index", "function": "calculate_red_black_board", "line": 593, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.844223", "level": "INFO", "logger": "root", "message": "有逾期的投资主体共有 16 个", "module": "index", "function": "calculate_red_black_board", "line": 602, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:12.844223", "level": "INFO", "logger": "root", "message": "逾期推进榜黑色标识: 集团, 金租, 财险", "module": "index", "function": "calculate_red_black_board", "line": 609, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:17.955881", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取用户 na10000014 的服务条线", "module": "auth", "function": "get_user_service_lines", "line": 23, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.078061", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "用户 na10000014 的服务条线: ['金租', '商租', '汽租']", "module": "auth", "function": "get_user_service_lines", "line": 40, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.081052", "level": "INFO", "logger": "app.api.endpoints.project", "message": "获取项目列表 - 参数: entity=, implementation=False, delayed=False", "module": "project", "function": "get_project_list", "line": 172, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.082047", "level": "INFO", "logger": "app.api.endpoints.project", "message": "调用云函数 getProjectList - 参数: {'queryString': {'entity': '', 'implementation': False, 'delayed': False}}", "module": "project", "function": "get_project_list", "line": 184, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.084089", "level": "INFO", "logger": "root", "message": "接收到请求: {'queryString': {'entity': '', 'implementation': False, 'delayed': False}}", "module": "index", "function": "main_handler", "line": 144, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.371902", "level": "INFO", "logger": "app.api.endpoints.project", "message": "云函数返回状态码: 200", "module": "project", "function": "get_project_list", "line": 186, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.378479", "level": "INFO", "logger": "app.api.endpoints.project", "message": "云函数返回 216 个项目", "module": "project", "function": "get_project_list", "line": 195, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.380468", "level": "INFO", "logger": "app.core.permissions", "message": "权限过滤 - 用户数据: {'UserID': 'na10000014', 'username': 'LiaoFang', 'name': '廖芳', 'role': 3.0, 'department_name': '业务二部', 'company_name': '数字金服', 'LaborCost': 20.0, 'service_lines': ['金租', '商租', '汽租']}", "module": "permissions", "function": "filter_projects_by_permission", "line": 108, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.381484", "level": "INFO", "logger": "app.core.permissions", "message": "权限过滤 - 用户条线权限: ['金租', '商租', '汽租']", "module": "permissions", "function": "filter_projects_by_permission", "line": 109, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.383984", "level": "INFO", "logger": "app.core.permissions", "message": "用户条线权限: ['金租', '商租', '汽租'], 过滤后项目数量: 50/216", "module": "permissions", "function": "filter_projects_by_permission", "line": 122, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.401352", "level": "INFO", "logger": "app.api.endpoints.project", "message": "用户 LiaoFang (role=3.0) 权限过滤后: 50 个项目，已添加档案统计信息", "module": "project", "function": "get_project_list", "line": 243, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.410640", "level": "INFO", "logger": "app.api.endpoints.options", "message": "调用云函数获取investment_entity数据，action=getInvestmentEntities", "module": "options", "function": "get_options", "line": 44, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.412998", "level": "INFO", "logger": "root", "message": "接收到请求: {'queryString': {'option_type': 'investment_entity', 'action': 'getInvestmentEntities'}}", "module": "index", "function": "main_handler", "line": 146, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.578320", "level": "INFO", "logger": "root", "message": "查询结果: 共返回 22 个投资主体", "module": "index", "function": "get_investment_entities", "line": 75, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T09:52:18.580326", "level": "INFO", "logger": "app.api.endpoints.options", "message": "成功获取investment_entity数据: {'code': 200, 'message': '获取投资主体列表成功', 'data': {'entities': ['不动产', '保理', '商租', '外部主体', '小贷', '征信', '担保', '汽租', '财险', '资管', '金服', '金租', '集团', '集团人力部', '集团保全部', '集团办公室', '集团协同部', '集团审计部', '集团战略部', '集团法规部', '集团财务部', '集团风控部']}}", "module": "options", "function": "get_options", "line": 52, "thread": 16712, "thread_name": "MainThread", "process": 4552}
{"timestamp": "2025-08-05T12:13:21.283466", "level": "INFO", "logger": "app.services.lightweight_vector_service", "message": "✅ 向量服务已禁用（提升启动速度）", "module": "lightweight_vector_service", "function": "__init__", "line": 42, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:21.287709", "level": "INFO", "logger": "app.services.document_cache", "message": "文档缓存数据库初始化完成", "module": "document_cache", "function": "_init_database", "line": 51, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:21.287709", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ 文档缓存初始化完成", "module": "document_parser", "function": "_init_performance_optimizations", "line": 121, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:21.288738", "level": "INFO", "logger": "app.services.document_parser", "message": "✅ Office COM对象池延迟初始化（提升启动速度）", "module": "document_parser", "function": "_init_performance_optimizations", "line": 125, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:21.715095", "level": "INFO", "logger": "app.services.vector_knowledge_service", "message": "✅ 向量知识库服务已禁用（提升启动速度）", "module": "vector_knowledge_service", "function": "__init__", "line": 38, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:22.402249", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI客户端", "module": "ai_client", "function": "__init__", "line": 43, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:22.402249", "level": "INFO", "logger": "app.core.ai_client", "message": "🎭 使用模拟AI响应", "module": "ai_client", "function": "_init_client", "line": 64, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:22.606398", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:22.728227", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:22.851644", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:22.938238", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:23.016123", "level": "INFO", "logger": "app.core.database", "message": "✅ 初始化数据库连接成功", "module": "database", "function": "_initialize_pool", "line": 71, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:27.090341", "level": "WARNING", "logger": "app.core.cache", "message": "Redis连接失败，使用内存缓存: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.", "module": "cache", "function": "__init__", "line": 99, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:27.253563", "level": "INFO", "logger": "app.core.websocket_manager", "message": "WebSocket后台任务已启动", "module": "websocket_manager", "function": "start_background_tasks", "line": 114, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:27.254600", "level": "INFO", "logger": "app.services.monitoring_service", "message": "系统监控已启动", "module": "monitoring_service", "function": "start_monitoring", "line": 50, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:13:28.279595", "level": "WARNING", "logger": "app.services.monitoring_service", "message": "系统告警: 内存使用率过高: 85.5%", "module": "monitoring_service", "function": "_check_alerts", "line": 203, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:14:29.307190", "level": "WARNING", "logger": "app.services.monitoring_service", "message": "系统告警: 内存使用率过高: 86.5%", "module": "monitoring_service", "function": "_check_alerts", "line": 203, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:15:30.338562", "level": "WARNING", "logger": "app.services.monitoring_service", "message": "系统告警: 内存使用率过高: 86.5%", "module": "monitoring_service", "function": "_check_alerts", "line": 203, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:15.300225", "level": "WARNING", "logger": "app.services.monitoring_service", "message": "系统告警: 内存使用率过高: 87.4%", "module": "monitoring_service", "function": "_check_alerts", "line": 203, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.044831", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取用户 na10000014 的服务条线", "module": "auth", "function": "get_user_service_lines", "line": 23, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.157633", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "用户 na10000014 的服务条线: ['金租', '商租', '汽租']", "module": "auth", "function": "get_user_service_lines", "line": 40, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.285876", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取到用户信息: {'UserID': 'na10000014', 'username': 'LiaoFang', 'name': '廖芳', 'role': 3.0, 'department_name': '业务二部', 'company_name': '数字金服', 'LaborCost': 20.0}", "module": "auth", "function": "get_me", "line": 151, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.286873", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取用户 na10000014 的服务条线", "module": "auth", "function": "get_user_service_lines", "line": 23, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.402258", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "用户 na10000014 的服务条线: ['金租', '商租', '汽租']", "module": "auth", "function": "get_user_service_lines", "line": 40, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.509956", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "获取用户 na10000014 的服务条线", "module": "auth", "function": "get_user_service_lines", "line": 23, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.633186", "level": "INFO", "logger": "app.api.endpoints.auth", "message": "用户 na10000014 的服务条线: ['金租', '商租', '汽租']", "module": "auth", "function": "get_user_service_lines", "line": 40, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.634186", "level": "INFO", "logger": "root", "message": "接收到请求: {'queryString': {}}", "module": "index", "function": "main_handler", "line": 656, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.720494", "level": "INFO", "logger": "root", "message": "执行查询: \n                    SELECT *, CAST(annual_investment_plan AS DECIMAL(10,2)) AS annual_investment_value\n                    FROM Project_Account_Book\n                ", "module": "index", "function": "main_handler", "line": 695, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.721009", "level": "INFO", "logger": "root", "message": "查询参数: []", "module": "index", "function": "main_handler", "line": 696, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.907593", "level": "INFO", "logger": "root", "message": "查询到的项目数量: 216", "module": "index", "function": "main_handler", "line": 700, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.907593", "level": "INFO", "logger": "root", "message": "查询ITBP团队成员，今天: 2025-08-05, 一年前: 2024-08-05", "module": "index", "function": "get_itbp_team_members", "line": 270, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.926306", "level": "INFO", "logger": "root", "message": "获取到 63 个ITBP团队成员", "module": "index", "function": "get_itbp_team_members", "line": 311, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.927305", "level": "INFO", "logger": "root", "message": "共有 21 个投资主体有团队成员数据", "module": "index", "function": "get_itbp_team_members", "line": 326, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.930302", "level": "INFO", "logger": "root", "message": "实施阶段项目: 诉讼管理系统（一期）, 编号: C202500006, 阶段: 项目实施, 状态: 未逾期, 投资金额: 83.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.930302", "level": "INFO", "logger": "root", "message": "实施阶段项目: 2024业务中台风控模块升级优化项目, 编号: C202500011, 阶段: 项目实施, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.931289", "level": "INFO", "logger": "root", "message": "实施阶段项目: 业务中台系统升级服务, 编号: C202500054, 阶段: 任务采购, 状态: 未逾期, 投资金额: 30.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.931289", "level": "INFO", "logger": "root", "message": "实施阶段项目: 个贷不良业务管理系统_电催, 编号: C202500090, 阶段: 项目实施, 状态: 未逾期, 投资金额: 100.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.932286", "level": "INFO", "logger": "root", "message": "实施阶段项目: 个贷不良业务管理系统_接征信, 编号: C202500091, 阶段: 项目实施, 状态: 未逾期, 投资金额: 125.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.932286", "level": "INFO", "logger": "root", "message": "实施阶段项目: IFRS17项目, 编号: C202500095, 阶段: 项目实施, 状态: 未逾期, 投资金额: 400.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.933284", "level": "INFO", "logger": "root", "message": "实施阶段项目: 农险一体化平台新建项目, 编号: C202500103, 阶段: 任务采购, 状态: 逾期, 投资金额: 350.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.933284", "level": "INFO", "logger": "root", "message": "实施阶段项目: 客户经营及理赔类系统建设, 编号: C202500104, 阶段: 任务采购, 状态: 未逾期, 投资金额: 230.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.934279", "level": "INFO", "logger": "root", "message": "实施阶段项目: 企业征信服务平台, 编号: C202500150, 阶段: 项目实施, 状态: 未逾期, 投资金额: 198.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.934279", "level": "INFO", "logger": "root", "message": "实施阶段项目: 库存融资业务系统后续开发, 编号: C202500132, 阶段: 项目实施, 状态: 未逾期, 投资金额: 120.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.934279", "level": "INFO", "logger": "root", "message": "实施阶段项目: 同盾调度平台, 编号: C202500133, 阶段: 项目实施, 状态: 未逾期, 投资金额: 115.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.935276", "level": "INFO", "logger": "root", "message": "实施阶段项目: 账号安全管理系统, 编号: C202500001, 阶段: 项目实施, 状态: 未逾期, 投资金额: 44.8", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.935276", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据中台（一期）及管理驾驶舱项目, 编号: C202500002, 阶段: 项目实施, 状态: 未逾期, 投资金额: 140.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.935276", "level": "INFO", "logger": "root", "message": "实施阶段项目: 融资管理系统项目, 编号: C202500003, 阶段: 项目结项, 状态: 未逾期, 投资金额: 64.4", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.935276", "level": "INFO", "logger": "root", "message": "实施阶段项目: EAS财务系统升级金蝶云星瀚项目, 编号: C202500004, 阶段: 已完成, 状态: 未逾期, 投资金额: 61.6", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.935276", "level": "INFO", "logger": "root", "message": "实施阶段项目: 新增业务中台不良资产过程管理及清收管控等功能项目, 编号: C202500005, 阶段: 项目结项, 状态: 未逾期, 投资金额: 45.1", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.936467", "level": "INFO", "logger": "root", "message": "实施阶段项目: 风险资产责任追究系统定制化开发升级项目, 编号: C202500007, 阶段: 项目验收, 状态: 逾期, 投资金额: 45.47", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.936467", "level": "INFO", "logger": "root", "message": "实施阶段项目: 金投集团四会管理系统优化升级项目, 编号: C202500008, 阶段: 已完成, 状态: 未逾期, 投资金额: 13.86", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.937464", "level": "INFO", "logger": "root", "message": "实施阶段项目: 司法大数据应用项目, 编号: C202500009, 阶段: 项目验收, 状态: 逾期, 投资金额: 34.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.937464", "level": "INFO", "logger": "root", "message": "实施阶段项目: 广投保理接入人行征信系统（24年未交付项目）, 编号: C202500016, 阶段: 项目验收, 状态: 未逾期, 投资金额: 56.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.938467", "level": "INFO", "logger": "root", "message": "实施阶段项目: 新资金系统, 编号: C202500020, 阶段: 项目实施, 状态: 未逾期, 投资金额: 88.2", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.939460", "level": "INFO", "logger": "root", "message": "实施阶段项目: 统一数据管理平台, 编号: C202500021, 阶段: 项目验收, 状态: 未逾期, 投资金额: 85.2", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.940454", "level": "INFO", "logger": "root", "message": "实施阶段项目: 移动核保, 编号: C202500022, 阶段: 项目实施, 状态: 未逾期, 投资金额: 76.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.940454", "level": "INFO", "logger": "root", "message": "实施阶段项目: 统一权限管理平台, 编号: C202500024, 阶段: 项目验收, 状态: 未逾期, 投资金额: 56.7", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.941453", "level": "INFO", "logger": "root", "message": "实施阶段项目: 移动行销, 编号: C202500025, 阶段: 项目验收, 状态: 未逾期, 投资金额: 93.6", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.941453", "level": "INFO", "logger": "root", "message": "实施阶段项目: 客户主数据系统, 编号: C202500026, 阶段: 项目实施, 状态: 逾期, 投资金额: 54.6", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.941453", "level": "INFO", "logger": "root", "message": "实施阶段项目: 统一用户管理平台, 编号: C202500027, 阶段: 项目实施, 状态: 未逾期, 投资金额: 49.7", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.941453", "level": "INFO", "logger": "root", "message": "实施阶段项目: 业务开发运维一体化平台, 编号: C202500029, 阶段: 项目验收, 状态: 未逾期, 投资金额: 42.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.942447", "level": "INFO", "logger": "root", "message": "实施阶段项目: 新费用控制系统, 编号: C202500030, 阶段: 项目实施, 状态: 未逾期, 投资金额: 41.4", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.942447", "level": "INFO", "logger": "root", "message": "实施阶段项目: HR人力资源系统, 编号: C202500031, 阶段: 项目实施, 状态: 未逾期, 投资金额: 35.76", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.943444", "level": "INFO", "logger": "root", "message": "实施阶段项目: 费用分摊模块（I4), 编号: C202500032, 阶段: 项目结项, 状态: 未逾期, 投资金额: 31.22", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.943444", "level": "INFO", "logger": "root", "message": "实施阶段项目: 诉讼管理系统, 编号: C202500033, 阶段: 项目验收, 状态: 未逾期, 投资金额: 26.28", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.943444", "level": "INFO", "logger": "root", "message": "实施阶段项目: 电子签章系统, 编号: C202500034, 阶段: 项目实施, 状态: 逾期, 投资金额: 23.9", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.943444", "level": "INFO", "logger": "root", "message": "实施阶段项目: AI影像防篡改与去重系统, 编号: C202500035, 阶段: 项目验收, 状态: 未逾期, 投资金额: 18.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.944440", "level": "INFO", "logger": "root", "message": "实施阶段项目: 新CTI系统, 编号: C202500037, 阶段: 项目实施, 状态: 未逾期, 投资金额: 17.07", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.944440", "level": "INFO", "logger": "root", "message": "实施阶段项目: 协同办公平台（OA系统）系统对接, 编号: C202500039, 阶段: 项目结项, 状态: 未逾期, 投资金额: 15.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.945436", "level": "INFO", "logger": "root", "message": "实施阶段项目: 绩效管理系统, 编号: C202500042, 阶段: 项目实施, 状态: 未逾期, 投资金额: 45.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.945436", "level": "INFO", "logger": "root", "message": "实施阶段项目: 征信系统建设项目, 编号: C202500043, 阶段: 项目实施, 状态: 未逾期, 投资金额: 25.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.945436", "level": "INFO", "logger": "root", "message": "实施阶段项目: 征信系统建设, 编号: C202500044, 阶段: 项目验收, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.946518", "level": "INFO", "logger": "root", "message": "实施阶段项目: 生猪监管二期项目, 编号: C202500045, 阶段: 项目验收, 状态: 逾期, 投资金额: 45.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.946784", "level": "INFO", "logger": "root", "message": "实施阶段项目: 财务资金类监管数据治理开发项目, 编号: C202500046, 阶段: 项目实施, 状态: 逾期, 投资金额: 115.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.946784", "level": "INFO", "logger": "root", "message": "实施阶段项目: 监管报送平台项目, 编号: C202500047, 阶段: 项目实施, 状态: 未逾期, 投资金额: 20.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.947787", "level": "INFO", "logger": "root", "message": "实施阶段项目: 核心业务系统优化升级项目, 编号: C202500050, 阶段: 任务采购, 状态: 未逾期, 投资金额: 110.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.947787", "level": "INFO", "logger": "root", "message": "实施阶段项目: 小贷数据分析服务平台项目, 编号: C202500051, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.947787", "level": "INFO", "logger": "root", "message": "实施阶段项目: 系统运维（费用类）, 编号: C202500053, 阶段: 任务采购, 状态: 未逾期, 投资金额: 16.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.948792", "level": "INFO", "logger": "root", "message": "实施阶段项目: 广投税务管理系统对接项目, 编号: C202500057, 阶段: 已完成, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.948792", "level": "INFO", "logger": "root", "message": "实施阶段项目: 集团网络安全设备加固, 编号: C202500058, 阶段: 任务采购, 状态: 逾期, 投资金额: 64.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.948792", "level": "INFO", "logger": "root", "message": "实施阶段项目: 客户信息共享平台项目, 编号: C202500061, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.949789", "level": "INFO", "logger": "root", "message": "实施阶段项目: 财务系统升级项目, 编号: C202500073, 阶段: 项目实施, 状态: 逾期, 投资金额: 80.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.949789", "level": "INFO", "logger": "root", "message": "实施阶段项目: 经营数据分析平台二期, 编号: C202500074, 阶段: 项目实施, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.949789", "level": "INFO", "logger": "root", "message": "实施阶段项目: 绩效考核管理系统, 编号: C202500075, 阶段: 项目实施, 状态: 未逾期, 投资金额: 10.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.949789", "level": "INFO", "logger": "root", "message": "实施阶段项目: 租赁业务中台系统新增功能开发项目, 编号: C202500076, 阶段: 任务采购, 状态: 逾期, 投资金额: 180.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.950785", "level": "INFO", "logger": "root", "message": "实施阶段项目: 绩效管理平台, 编号: C202500085, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.950785", "level": "INFO", "logger": "root", "message": "实施阶段项目: 各类系统集成接口开发项目, 编号: C202500086, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.950785", "level": "INFO", "logger": "root", "message": "实施阶段项目: 供应链金融服务平台优化项目, 编号: C202500087, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.950785", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_日志管理, 编号: C202500096, 阶段: 任务采购, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.951782", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_国产系统及数据库, 编号: C202500098, 阶段: 任务采购, 状态: 逾期, 投资金额: 70.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.951782", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_监控, 编号: C202500099, 阶段: 任务采购, 状态: 未逾期, 投资金额: 64.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.951782", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_网管, 编号: C202500100, 阶段: 任务采购, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.952780", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施和安全类软件项目_视频会议, 编号: C202500101, 阶段: 项目验收, 状态: 逾期, 投资金额: 90.8", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.953661", "level": "INFO", "logger": "root", "message": "实施阶段项目: 风控数字化建设, 编号: C202500105, 阶段: 任务采购, 状态: 未逾期, 投资金额: 200.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.954165", "level": "INFO", "logger": "root", "message": "实施阶段项目: 平台支撑类应用系统建设, 编号: C202500106, 阶段: 任务采购, 状态: 未逾期, 投资金额: 200.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.954688", "level": "INFO", "logger": "root", "message": "实施阶段项目: 安责险事故预防管理系统, 编号: C202500107, 阶段: 项目实施, 状态: 未逾期, 投资金额: 54.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.955204", "level": "INFO", "logger": "root", "message": "实施阶段项目: 经营分析平台二期建设, 编号: C202500114, 阶段: 项目实施, 状态: 未逾期, 投资金额: 30.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.955204", "level": "INFO", "logger": "root", "message": "实施阶段项目: 智能风控系统, 编号: C202500116, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.955723", "level": "INFO", "logger": "root", "message": "实施阶段项目: 三会会议管理系统, 编号: C202500120, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.956244", "level": "INFO", "logger": "root", "message": "实施阶段项目: 智能风控平台项目, 编号: C202500129, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.956244", "level": "INFO", "logger": "root", "message": "实施阶段项目: 2024年系统开发未结项, 编号: C202500131, 阶段: 项目实施, 状态: 未逾期, 投资金额: 162.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.956762", "level": "INFO", "logger": "root", "message": "实施阶段项目: 财务中台, 编号: C202500135, 阶段: 任务采购, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.956762", "level": "INFO", "logger": "root", "message": "实施阶段项目: 24年系统开发未结项, 编号: C202500140, 阶段: 项目实施, 状态: 未逾期, 投资金额: 100.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.957276", "level": "INFO", "logger": "root", "message": "实施阶段项目: 智能风控系统, 编号: C202500141, 阶段: 项目验收, 状态: 未逾期, 投资金额: 50.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.957793", "level": "INFO", "logger": "root", "message": "实施阶段项目: BI系统建设, 编号: C202500143, 阶段: 项目实施, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.957793", "level": "INFO", "logger": "root", "message": "实施阶段项目: 智能外呼系统, 编号: C202500144, 阶段: 项目实施, 状态: 未逾期, 投资金额: 30.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.958310", "level": "INFO", "logger": "root", "message": "实施阶段项目: 财务系统开发, 编号: C202500145, 阶段: 项目实施, 状态: 逾期, 投资金额: 20.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.958310", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据仓库, 编号: C202500147, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.958829", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据建模系统, 编号: C202500148, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.959347", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据治理系统, 编号: C202500149, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.959869", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据质量分析系统, 编号: C202500151, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.959869", "level": "INFO", "logger": "root", "message": "实施阶段项目: 软件测评和安全测评服务采购, 编号: C202500152, 阶段: 项目实施, 状态: 未逾期, 投资金额: 25.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.960383", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件项目尾款, 编号: C202500156, 阶段: 项目验收, 状态: 未逾期, 投资金额: 186.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.960888", "level": "INFO", "logger": "root", "message": "实施阶段项目: 安全设备替换及升级项目（2024）, 编号: C202500157, 阶段: 任务采购, 状态: 未逾期, 投资金额: 48.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.960888", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_网络设备, 编号: C202500161, 阶段: 任务采购, 状态: 未逾期, 投资金额: 72.9", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.961412", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_灾备扩容, 编号: C202500162, 阶段: 任务采购, 状态: 未逾期, 投资金额: 148.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.961412", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_应用监控（硬件）, 编号: C202500163, 阶段: 任务采购, 状态: 逾期, 投资金额: 36.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.961412", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_负载均衡扩容, 编号: C202500165, 阶段: 任务采购, 状态: 未逾期, 投资金额: 45.6", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.962410", "level": "INFO", "logger": "root", "message": "实施阶段项目: 基础设施硬件(含安全)项目_视频会议硬件, 编号: C202500166, 阶段: 项目验收, 状态: 逾期, 投资金额: 98.29", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.962913", "level": "INFO", "logger": "root", "message": "实施阶段项目: 超融合平台扩容, 编号: C202500170, 阶段: 项目实施, 状态: 未逾期, 投资金额: 84.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.963433", "level": "INFO", "logger": "root", "message": "实施阶段项目: 服务器采购, 编号: C202500172, 阶段: 已完成, 状态: 未逾期, 投资金额: 33.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.963940", "level": "INFO", "logger": "root", "message": "实施阶段项目: 网络安全设备采购, 编号: C202500173, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.964459", "level": "INFO", "logger": "root", "message": "实施阶段项目: 数据库服务器采购, 编号: C202500178, 阶段: 项目实施, 状态: 未逾期, 投资金额: 40.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.964459", "level": "INFO", "logger": "root", "message": "实施阶段项目: 签名验签服务器采购, 编号: C202500179, 阶段: 已完成, 状态: 未逾期, 投资金额: 20.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.965051", "level": "INFO", "logger": "root", "message": "实施阶段项目: 电子设备采购, 编号: C202500181, 阶段: 已完成, 状态: 未逾期, 投资金额: 5.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.965051", "level": "INFO", "logger": "root", "message": "实施阶段项目: 办公设备, 编号: C202500182, 阶段: 已完成, 状态: 未逾期, 投资金额: 4.9", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.965553", "level": "INFO", "logger": "root", "message": "实施阶段项目: 印控系统升级, 编号: C202500186, 阶段: 项目实施, 状态: 未逾期, 投资金额: 20.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.965553", "level": "INFO", "logger": "root", "message": "实施阶段项目: 国产化电脑替代, 编号: C202500187, 阶段: 任务采购, 状态: 未逾期, 投资金额: 8.3", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.965553", "level": "INFO", "logger": "root", "message": "实施阶段项目: 征信数据处理平台, 编号: C202500189, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.966066", "level": "INFO", "logger": "root", "message": "实施阶段项目: 公共数据处理平台, 编号: C202500190, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.966578", "level": "INFO", "logger": "root", "message": "实施阶段项目: 企业征信服务平台, 编号: C202500191, 阶段: 已完成, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.967195", "level": "INFO", "logger": "root", "message": "实施阶段项目: 办公电子设备采购, 编号: C202500192, 阶段: 任务采购, 状态: 逾期, 投资金额: 41.5", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.967195", "level": "INFO", "logger": "root", "message": "实施阶段项目: 国产化电脑, 编号: C202500193, 阶段: 项目验收, 状态: 未逾期, 投资金额: 25.2", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.967698", "level": "INFO", "logger": "root", "message": "实施阶段项目: 桂惠农项目（“金色乡村”广西农村信用信息系统）, 编号: C202500195, 阶段: 项目验收, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.968214", "level": "INFO", "logger": "root", "message": "实施阶段项目: 桂惠通、桂惠农与智桂通直连项目, 编号: C202500196, 阶段: 项目验收, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:40.968728", "level": "INFO", "logger": "root", "message": "实施阶段项目: 北部湾经济区金融服务平台二期, 编号: C202500204, 阶段: 项目验收, 状态: 未逾期, 投资金额: 0.0", "module": "index", "function": "calculate_red_black_board", "line": 471, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.151104", "level": "INFO", "logger": "root", "message": "获取到 701 条工时记录，年份：2025", "module": "index", "function": "get_labor_costs_by_entity", "line": 357, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.171369", "level": "INFO", "logger": "root", "message": "获取到 216 条项目记录用于关联投资主体信息", "module": "index", "function": "get_labor_costs_by_entity", "line": 366, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.172405", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.173455", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.173455", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.174359", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.174359", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.175363", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.175363", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.175363", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.176352", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.176352", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.176352", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.176352", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.177349", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.177349", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.177349", "level": "WARNING", "logger": "root", "message": "项目 C202500211 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.178346", "level": "WARNING", "logger": "root", "message": "项目 C202500211 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.178346", "level": "WARNING", "logger": "root", "message": "项目 C202500211 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.178346", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.178346", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.179342", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.179342", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.179342", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.179342", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.180349", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.180349", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.180349", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.181345", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.181345", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.181345", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.181345", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.181345", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.182385", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.182385", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.182385", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.183328", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.183328", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.183328", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.184326", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.184326", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.184326", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.184326", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.185323", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.185323", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.185323", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.186318", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.186318", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.187316", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.187316", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.188312", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.188312", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.188312", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.189309", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.189309", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.189309", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.190308", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.190308", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.190308", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.190308", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.190308", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.191302", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.191302", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.191302", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.191302", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.192299", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.192299", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.192299", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.192299", "level": "WARNING", "logger": "root", "message": "项目 C202500211 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.193295", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.193295", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.193295", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.194293", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.194293", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.194293", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.194293", "level": "WARNING", "logger": "root", "message": "项目 C202500213 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.195299", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.195299", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.195299", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.196287", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.196287", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.196287", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.196287", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.6, 'month': datetime.date(2025, 5, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.197283", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.5, 'month': datetime.date(2025, 4, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.197283", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.4, 'month': datetime.date(2025, 3, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.197283", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.3, 'month': datetime.date(2025, 2, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.197283", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'U20250519081549', 'working_hours': 0.2, 'month': datetime.date(2025, 1, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.198278", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.6, 'month': datetime.date(2025, 5, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.198278", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.5, 'month': datetime.date(2025, 4, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.198278", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.4, 'month': datetime.date(2025, 3, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.199276", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.3, 'month': datetime.date(2025, 2, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.199276", "level": "WARNING", "logger": "root", "message": "工时记录未找到对应项目或项目编号为空，跳过: {'project_code': 'PRJ2025052594FF', 'UserID': 'na10000003', 'working_hours': 0.2, 'month': datetime.date(2025, 1, 1), 'LaborCost': None}", "module": "index", "function": "get_labor_costs_by_entity", "line": 375, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.199276", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.199276", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.200276", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.200276", "level": "WARNING", "logger": "root", "message": "项目 C202500209 没有设置投资主体，跳过", "module": "index", "function": "get_labor_costs_by_entity", "line": 381, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.200276", "level": "INFO", "logger": "root", "message": "计算完成，共有 18 个投资主体的人工费数据", "module": "index", "function": "get_labor_costs_by_entity", "line": 419, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.201271", "level": "INFO", "logger": "root", "message": "获取到 18 个投资主体的人工费数据", "module": "index", "function": "calculate_red_black_board", "line": 476, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.201271", "level": "INFO", "logger": "root", "message": "集团战略部 实施阶段项目数: 3", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.201271", "level": "INFO", "logger": "root", "message": "投资主体: 集团战略部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.202267", "level": "INFO", "logger": "root", "message": "  总项目数: 6, 总预算: 293", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.202267", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 75", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.202267", "level": "INFO", "logger": "root", "message": "  实施项目数: 3, 实施预算: 185", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.202267", "level": "INFO", "logger": "root", "message": "  当年人次合计: 11.6, 人工费: 20万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.202267", "level": "INFO", "logger": "root", "message": "集团法规部 实施阶段项目数: 2", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.203611", "level": "INFO", "logger": "root", "message": "投资主体: 集团法规部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.203611", "level": "INFO", "logger": "root", "message": "  总项目数: 3, 总预算: 150", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.203611", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 34", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.204767", "level": "INFO", "logger": "root", "message": "  实施项目数: 2, 实施预算: 118", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.204767", "level": "INFO", "logger": "root", "message": "  当年人次合计: 14.6, 人工费: 23万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.204767", "level": "INFO", "logger": "root", "message": "集团 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.204767", "level": "INFO", "logger": "root", "message": "投资主体: 集团", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.205767", "level": "INFO", "logger": "root", "message": "  总项目数: 7, 总预算: 543", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.205767", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 364", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.205767", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 64", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.205767", "level": "INFO", "logger": "root", "message": "  当年人次合计: 4.5, 人工费: 7万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.206764", "level": "INFO", "logger": "root", "message": "集团风控部 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.206764", "level": "INFO", "logger": "root", "message": "投资主体: 集团风控部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.206764", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 90", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.206764", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.207760", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 40", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.207760", "level": "INFO", "logger": "root", "message": "  当年人次合计: 1.0, 人工费: 2万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.207760", "level": "INFO", "logger": "root", "message": "集团财务部 实施阶段项目数: 3", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.207760", "level": "INFO", "logger": "root", "message": "投资主体: 集团财务部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.208757", "level": "INFO", "logger": "root", "message": "  总项目数: 6, 总预算: 496", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.208757", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 290", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.208757", "level": "INFO", "logger": "root", "message": "  实施项目数: 3, 实施预算: 176", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.208757", "level": "INFO", "logger": "root", "message": "  当年人次合计: 4.14, 人工费: 7万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.209754", "level": "INFO", "logger": "root", "message": "保理 实施阶段项目数: 5", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.209754", "level": "INFO", "logger": "root", "message": "投资主体: 保理", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.209754", "level": "INFO", "logger": "root", "message": "  总项目数: 15, 总预算: 489", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.209754", "level": "INFO", "logger": "root", "message": "  逾期项目数: 6, 逾期预算: 304", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.210751", "level": "INFO", "logger": "root", "message": "  实施项目数: 5, 实施预算: 64", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.210751", "level": "INFO", "logger": "root", "message": "  当年人次合计: 18.14, 人工费: 32万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.210751", "level": "INFO", "logger": "root", "message": "不动产 实施阶段项目数: 2", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.210751", "level": "INFO", "logger": "root", "message": "投资主体: 不动产", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.211747", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 55", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.211747", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.211747", "level": "INFO", "logger": "root", "message": "  实施项目数: 2, 实施预算: 55", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.211747", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.212825", "level": "INFO", "logger": "root", "message": "资管 实施阶段项目数: 2", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.212825", "level": "INFO", "logger": "root", "message": "投资主体: 资管", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.213329", "level": "INFO", "logger": "root", "message": "  总项目数: 6, 总预算: 591", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.213329", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 195", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.213329", "level": "INFO", "logger": "root", "message": "  实施项目数: 2, 实施预算: 225", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.213329", "level": "INFO", "logger": "root", "message": "  当年人次合计: 1.3, 人工费: 2万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.214330", "level": "INFO", "logger": "root", "message": "财险 实施阶段项目数: 35", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.214330", "level": "INFO", "logger": "root", "message": "投资主体: 财险", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.214330", "level": "INFO", "logger": "root", "message": "  总项目数: 54, 总预算: 4828", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.215325", "level": "INFO", "logger": "root", "message": "  逾期项目数: 14, 逾期预算: 1086", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.215325", "level": "INFO", "logger": "root", "message": "  实施项目数: 35, 实施预算: 3175", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.215325", "level": "INFO", "logger": "root", "message": "  当年人次合计: 5.11, 人工费: 9万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.215325", "level": "INFO", "logger": "root", "message": "征信 实施阶段项目数: 10", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.216322", "level": "INFO", "logger": "root", "message": "投资主体: 征信", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.216322", "level": "INFO", "logger": "root", "message": "  总项目数: 10, 总预算: 264", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.216322", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 42", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.216322", "level": "INFO", "logger": "root", "message": "  实施项目数: 10, 实施预算: 264", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.216322", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0.05, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.217320", "level": "INFO", "logger": "root", "message": "商租 实施阶段项目数: 10", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.217320", "level": "INFO", "logger": "root", "message": "投资主体: 商租", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.217320", "level": "INFO", "logger": "root", "message": "  总项目数: 17, 总预算: 939", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.217320", "level": "INFO", "logger": "root", "message": "  逾期项目数: 3, 逾期预算: 295", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.218317", "level": "INFO", "logger": "root", "message": "  实施项目数: 10, 实施预算: 360", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.218317", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.218317", "level": "INFO", "logger": "root", "message": "汽租 实施阶段项目数: 9", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.219314", "level": "INFO", "logger": "root", "message": "投资主体: 汽租", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.219820", "level": "INFO", "logger": "root", "message": "  总项目数: 16, 总预算: 1001", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.220342", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 180", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.220415", "level": "INFO", "logger": "root", "message": "  实施项目数: 9, 实施预算: 634", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.220921", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0.51, 人工费: 1万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.221001", "level": "INFO", "logger": "root", "message": "集团保全部 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.221506", "level": "INFO", "logger": "root", "message": "投资主体: 集团保全部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.221506", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 125", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.222076", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 80", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.222076", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 45", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.222076", "level": "INFO", "logger": "root", "message": "  当年人次合计: 9.28, 人工费: 15万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.222583", "level": "INFO", "logger": "root", "message": "集团审计部 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.222583", "level": "INFO", "logger": "root", "message": "投资主体: 集团审计部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.223097", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 90", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.223097", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 90", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.223633", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 45", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.223633", "level": "INFO", "logger": "root", "message": "  当年人次合计: 5.36, 人工费: 8万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.224171", "level": "INFO", "logger": "root", "message": "集团办公室 实施阶段项目数: 1", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.224171", "level": "INFO", "logger": "root", "message": "投资主体: 集团办公室", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.224697", "level": "INFO", "logger": "root", "message": "  总项目数: 1, 总预算: 14", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.224697", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.225219", "level": "INFO", "logger": "root", "message": "  实施项目数: 1, 实施预算: 14", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.225219", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0.9, 人工费: 2万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.225747", "level": "INFO", "logger": "root", "message": "集团人力部 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.225747", "level": "INFO", "logger": "root", "message": "投资主体: 集团人力部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.226276", "level": "INFO", "logger": "root", "message": "  总项目数: 2, 总预算: 38", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.226276", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.226276", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.226800", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.226800", "level": "INFO", "logger": "root", "message": "金租 实施阶段项目数: 6", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.227313", "level": "INFO", "logger": "root", "message": "投资主体: 金租", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.227313", "level": "INFO", "logger": "root", "message": "  总项目数: 17, 总预算: 911", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.227313", "level": "INFO", "logger": "root", "message": "  逾期项目数: 9, 逾期预算: 541", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.227905", "level": "INFO", "logger": "root", "message": "  实施项目数: 6, 实施预算: 455", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.228410", "level": "INFO", "logger": "root", "message": "  当年人次合计: 9.5, 人工费: 16万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.228410", "level": "INFO", "logger": "root", "message": "小贷 实施阶段项目数: 4", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.228945", "level": "INFO", "logger": "root", "message": "投资主体: 小贷", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.228945", "level": "INFO", "logger": "root", "message": "  总项目数: 9, 总预算: 195", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.229482", "level": "INFO", "logger": "root", "message": "  逾期项目数: 2, 逾期预算: 35", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.229482", "level": "INFO", "logger": "root", "message": "  实施项目数: 4, 实施预算: 126", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.230008", "level": "INFO", "logger": "root", "message": "  当年人次合计: 2.27, 人工费: 5万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.230008", "level": "INFO", "logger": "root", "message": "集团协同部 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.230008", "level": "INFO", "logger": "root", "message": "投资主体: 集团协同部", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.230008", "level": "INFO", "logger": "root", "message": "  总项目数: 1, 总预算: 80", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.231011", "level": "INFO", "logger": "root", "message": "  逾期项目数: 1, 逾期预算: 80", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.231011", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.231011", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.231011", "level": "INFO", "logger": "root", "message": "担保 实施阶段项目数: 4", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.232008", "level": "INFO", "logger": "root", "message": "投资主体: 担保", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.232008", "level": "INFO", "logger": "root", "message": "  总项目数: 17, 总预算: 553", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.232008", "level": "INFO", "logger": "root", "message": "  逾期项目数: 6, 逾期预算: 255", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.232008", "level": "INFO", "logger": "root", "message": "  实施项目数: 4, 实施预算: 50", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.233004", "level": "INFO", "logger": "root", "message": "  当年人次合计: 22.32, 人工费: 44万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.233004", "level": "INFO", "logger": "root", "message": "金服 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.233004", "level": "INFO", "logger": "root", "message": "投资主体: 金服", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.233004", "level": "INFO", "logger": "root", "message": "  总项目数: 3, 总预算: 30", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.234000", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.234000", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.234000", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0.38, 人工费: 1万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.234998", "level": "INFO", "logger": "root", "message": "外部主体 实施阶段项目数: 3", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.234998", "level": "INFO", "logger": "root", "message": "外部主体: 实施项目无投资金额，使用估算: 0万元 (总投资0万元的21.4%)", "module": "index", "function": "calculate_red_black_board", "line": 525, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.234998", "level": "INFO", "logger": "root", "message": "投资主体: 外部主体", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.234998", "level": "INFO", "logger": "root", "message": "  总项目数: 14, 总预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.234998", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.235995", "level": "INFO", "logger": "root", "message": "  实施项目数: 3, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.235995", "level": "INFO", "logger": "root", "message": "  当年人次合计: 39.9, 人工费: 67万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.235995", "level": "INFO", "logger": "root", "message": "None 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.237285", "level": "INFO", "logger": "root", "message": "投资主体: None", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.237285", "level": "INFO", "logger": "root", "message": "  总项目数: 3, 总预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.237285", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.238285", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.238285", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.238285", "level": "INFO", "logger": "root", "message": " 实施阶段项目数: 0", "module": "index", "function": "calculate_red_black_board", "line": 481, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.239282", "level": "INFO", "logger": "root", "message": "投资主体: ", "module": "index", "function": "calculate_red_black_board", "line": 539, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.239282", "level": "INFO", "logger": "root", "message": "  总项目数: 1, 总预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 540, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.239282", "level": "INFO", "logger": "root", "message": "  逾期项目数: 0, 逾期预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 541, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.239282", "level": "INFO", "logger": "root", "message": "  实施项目数: 0, 实施预算: 0", "module": "index", "function": "calculate_red_black_board", "line": 542, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.240278", "level": "INFO", "logger": "root", "message": "  当年人次合计: 0, 人工费: 0万元", "module": "index", "function": "calculate_red_black_board", "line": 543, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.240278", "level": "INFO", "logger": "root", "message": "没有逾期的投资主体共有 8 个", "module": "index", "function": "calculate_red_black_board", "line": 586, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.240278", "level": "INFO", "logger": "root", "message": "按进度推进榜红色标识: 不动产, 集团风控部, 集团办公室", "module": "index", "function": "calculate_red_black_board", "line": 593, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.240278", "level": "INFO", "logger": "root", "message": "有逾期的投资主体共有 16 个", "module": "index", "function": "calculate_red_black_board", "line": 602, "thread": 13560, "thread_name": "MainThread", "process": 37708}
{"timestamp": "2025-08-05T12:17:41.240278", "level": "INFO", "logger": "root", "message": "逾期推进榜黑色标识: 集团, 金租, 财险", "module": "index", "function": "calculate_red_black_board", "line": 609, "thread": 13560, "thread_name": "MainThread", "process": 37708}

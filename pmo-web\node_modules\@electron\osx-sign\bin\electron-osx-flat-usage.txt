
NAME
  electron-osx-flat -- product building for Electron apps

SYNOPSIS
  electron-osx-flat app [options ...]

DESCRIPTION
  app
    Path to the application package.
    Needs file extension ``.app''.

  --help
    <PERSON> to display all commands.

  --identity=identity
    Name of certificate to use when signing.
    Default to selected with respect to --platform from --keychain specified or keychain by system default.

  --identityValidation, --no-identityValidation
    Flag to enable/disable validation for the signing identity.

  --install=install-path
    Path to install the bundle.
    Default to ``/Applications''.

  --keychain=keychain
    The keychain name.
    Default to system default keychain.

  --platform=platform
    Build platform of Electron.
    Allowed values: ``darwin'', ``mas''.
    Default to auto detect from application bundle.

  --pkg
    Path to the output the flattened package.
    Needs file extension ``.pkg''.

  --scripts
    Path to a directory containing pre and/or post install scripts.

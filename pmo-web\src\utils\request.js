import axios from 'axios'
import { ElMessage } from 'element-plus'
import { checkToken, handleTokenInvalid } from './auth'

// 请求缓存
const requestCache = new Map()
const cacheTimeout = 5 * 60 * 1000 // 5分钟缓存

// 请求队列管理
const requestQueue = new Map()
let maxConcurrentRequests = 10
let activeRequests = 0

// 获取配置
const getConfig = () => {
  // 优先使用外部配置文件
  if (window.PMO_CONFIG) {
    return window.PMO_CONFIG
  }

  // 回退到环境变量
  return {
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
    REQUEST_TIMEOUT: 30000,
    MAX_CONCURRENT_REQUESTS: 10
  }
}

const config = getConfig()

// 创建axios实例 - 优化配置
const service = axios.create({
  baseURL: config.API_BASE_URL + '/v1',
  timeout: config.REQUEST_TIMEOUT || 30000,
  maxContentLength: 50 * 1024 * 1024, // 50MB
  maxBodyLength: 50 * 1024 * 1024, // 50MB
  // 启用请求压缩
  headers: {
    'Accept-Encoding': 'gzip, deflate, br',
    'Content-Type': 'application/json'
  }
})

// 更新并发请求限制
if (config.MAX_CONCURRENT_REQUESTS) {
  maxConcurrentRequests = config.MAX_CONCURRENT_REQUESTS
}

// 记录是否正在刷新token
let isRefreshing = false
// 等待刷新token的请求队列
let requests = []
// 记录已重试的请求，避免无限重试
const retriedRequests = new Map()

// 缓存工具函数
const getCacheKey = (config) => {
  const { method, url, params, data } = config
  return `${method}:${url}:${JSON.stringify(params || {})}:${JSON.stringify(data || {})}`
}

const getFromCache = (key) => {
  const cached = requestCache.get(key)
  if (cached && Date.now() - cached.timestamp < cacheTimeout) {
    return cached.data
  }
  requestCache.delete(key)
  return null
}

const setCache = (key, data) => {
  requestCache.set(key, {
    data,
    timestamp: Date.now()
  })
}

// 请求队列管理
const addToQueue = (config) => {
  return new Promise((resolve, reject) => {
    const queueKey = getCacheKey(config)

    // 如果已有相同请求在队列中，直接返回该Promise
    if (requestQueue.has(queueKey)) {
      return requestQueue.get(queueKey)
    }

    const promise = new Promise((res, rej) => {
      const executeRequest = async () => {
        try {
          activeRequests++
          const response = await axios(config)
          res(response)
        } catch (error) {
          rej(error)
        } finally {
          activeRequests--
          requestQueue.delete(queueKey)
          // 处理队列中的下一个请求
          processQueue()
        }
      }

      // 如果当前活跃请求数小于最大并发数，立即执行
      if (activeRequests < maxConcurrentRequests) {
        executeRequest()
      } else {
        // 否则等待
        setTimeout(() => {
          if (activeRequests < maxConcurrentRequests) {
            executeRequest()
          }
        }, 100)
      }
    })

    requestQueue.set(queueKey, promise)
    resolve(promise)
  })
}

const processQueue = () => {
  // 处理等待中的请求
  if (activeRequests < maxConcurrentRequests && requestQueue.size > 0) {
    // 这里可以添加更复杂的队列处理逻辑
  }
}

// 处理可能的中文编码问题
const fixEncoding = (obj) => {
  if (!obj) return obj
  
  if (typeof obj === 'string') {
    // 尝试修复可能的编码问题
    try {
      // 如果字符串中包含乱码特征，尝试解码
      if (/\\u[0-9a-f]{4}/i.test(obj) || /%[0-9a-f]{2}/i.test(obj)) {
        return decodeURIComponent(obj)
      }
      
      // 检查是否包含常见的中文乱码特征
      if (/é|è|ä|å|æ|ç|å\u0085/.test(obj)) {
        // 尝试使用TextDecoder解码
        try {
          // 先将可能的UTF-8字符串转为字节数组
          const encoder = new TextEncoder();
          const decoder = new TextDecoder('utf-8');
          return decoder.decode(encoder.encode(obj));
        } catch (decodeErr) {
          console.warn('TextDecoder解码失败:', decodeErr);
          // 保留原字符串
          return obj;
        }
      }
    } catch (e) {
      console.warn('解码字符串失败:', e)
    }
    return obj
  }
  
  if (typeof obj !== 'object') return obj
  
  if (Array.isArray(obj)) {
    return obj.map(item => fixEncoding(item))
  }
  
  const result = {}
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      result[key] = fixEncoding(obj[key])
    }
  }
  return result
}

// 请求拦截器 - 优化版本
service.interceptors.request.use(
  (config) => {
    // 检查缓存（仅对GET请求）
    if (config.method === 'get' && !config.noCache) {
      const cacheKey = getCacheKey(config)
      const cachedData = getFromCache(cacheKey)
      if (cachedData) {
        console.log('使用缓存数据:', config.url)
        return Promise.resolve({
          data: cachedData,
          status: 200,
          statusText: 'OK',
          headers: {},
          config,
          fromCache: true
        })
      }
    }

    // 从localStorage获取token，只有在不是noAuth请求时才添加token
    const token = localStorage.getItem('token')
    if (token && !config.noAuth) {
      // 添加token到请求头
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 标记请求是否已重试
    if (!config.retryAttempt) {
      config.retryAttempt = 0

      // 为请求生成唯一ID，用于跟踪重试
      if (!config.requestId) {
        config.requestId = `${config.method}-${config.url}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`
      }
    }

    // 添加性能标记
    config.startTime = Date.now()

    // 添加请求日志（仅在开发环境）
    if (import.meta.env.DEV) {
      console.log('发送请求:', {
        url: config.url,
        method: config.method,
        params: config.params,
        data: config.data,
        hasToken: !config.noAuth && !!token,
        noAuth: !!config.noAuth,
        retryAttempt: config.retryAttempt,
        requestId: config.requestId,
        activeRequests: activeRequests
      })
    }

    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器 - 优化版本
service.interceptors.response.use(
  (response) => {
    let res = response.data
    const config = response.config

    // 计算请求耗时
    const duration = config.startTime ? Date.now() - config.startTime : 0

    // 添加响应日志（仅在开发环境）
    if (import.meta.env.DEV) {
      console.log('收到响应:', {
        url: config.url,
        status: response.status,
        duration: `${duration}ms`,
        data: res,
        requestId: config.requestId,
        fromCache: response.fromCache
      })
    }

    // 如果是从缓存返回的数据，直接返回
    if (response.fromCache) {
      return res
    }

    // 处理可能的编码问题
    res = fixEncoding(res)

    // 缓存GET请求的成功响应
    if (config.method === 'get' && !config.noCache && response.status === 200) {
      const cacheKey = getCacheKey(config)
      setCache(cacheKey, res)
    }
    
    // 如果响应成功但业务状态码不是200
    // 兼容不同的响应格式：{code: 200} 或 {success: true}
    if (res.code !== undefined && res.code !== 200) {
      // 401: 未授权，可能是token过期
      if (res.code === 401) {
        // 记录日志
        console.warn('接收到401未授权响应，token可能已过期', config.requestId)

        // 直接显示错误消息，不进行重试
        ElMessage.error('请求失败，可能是登录已过期')
        return Promise.reject(new Error(res.message || '登录已过期'))
      }
      
      // 其他错误正常提示
      ElMessage({
        message: res.message || '请求失败',
        type: 'error',
        duration: 5 * 1000
      })
      
      return Promise.reject(new Error(res.message || '未知错误'))
    }
    
    // 清除请求重试记录
    if (config.requestId) {
      retriedRequests.delete(config.requestId)
    }

    // 如果响应包含 success 字段，直接返回
    if (res.success !== undefined) {
      return res
    }

    return res
  },
  (error) => {
    console.error('响应错误:', error)
    
    // 获取请求配置
    const config = error.config || {}
    
    // 处理HTTP错误状态码
    if (error.response) {
      const status = error.response.status
      let message = '请求失败'
      
      if (status === 401) {
        // 记录日志
        console.warn('接收到401未授权HTTP状态，token可能已过期', config.requestId)

        // 判断是否是options接口
        const isOptionsApi = config.url && config.url.includes('/options')

        // 如果是options接口，让组件自己处理
        if (isOptionsApi) {
          return Promise.reject(error)
        }

        // 其他接口，只显示错误消息，不自动跳转
        message = '请求失败，可能是登录已过期'
        ElMessage.error(message)
        return Promise.reject(error)
      } else if (status === 403) {
        message = '拒绝访问'
        ElMessage({
          message: error.response.data?.message || message,
          type: 'error',
          duration: 5 * 1000
        })
      } else if (status === 404) {
        message = '请求的资源不存在'
        ElMessage({
          message: error.response.data?.message || message,
          type: 'error',
          duration: 5 * 1000
        })
      } else if (status === 500) {
        message = '服务器内部错误'
        ElMessage({
          message: error.response.data?.message || message,
          type: 'error',
          duration: 5 * 1000
        })
      } else {
        ElMessage({
          message: error.response.data?.message || message,
          type: 'error',
          duration: 5 * 1000
        })
      }
    } else {
      ElMessage({
        message: error.message || '请求失败，请检查网络连接',
        type: 'error',
        duration: 5 * 1000
      })
    }
    
    return Promise.reject(error)
  }
)

export default service 